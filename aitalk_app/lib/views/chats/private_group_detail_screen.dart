import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../core/services/database_service.dart';
import '../../core/device/device_manager.dart';
import '../../core/user/user_profile.dart';

/// 私有群详情页，展示群成员、群信息，并提供编辑、删除等操作。
class PrivateGroupDetailScreen extends StatefulWidget {
  final String conversationId;
  final String groupName;
  final int initialChannel;

  const PrivateGroupDetailScreen({
    super.key,
    required this.conversationId,
    required this.groupName,
    required this.initialChannel,
  });

  @override
  State<PrivateGroupDetailScreen> createState() =>
      _PrivateGroupDetailScreenState();
}

class _PrivateGroupDetailScreenState extends State<PrivateGroupDetailScreen> {
  late int _selectedChannel;
  late String _groupName;
  late String _password;
  List<Map<String, dynamic>> _members = [];
  bool _isLoading = true;
  bool _isPasswordVisible = false; // 密码是否可见
  late final ValueNotifier<int> _groupChangedListener;

  @override
  void initState() {
    super.initState();
    _selectedChannel = widget.initialChannel;
    _groupName = widget.groupName;
    _password = '';

    // 监听数据库变更，实时更新群成员列表
    _groupChangedListener = DatabaseService.groupChangedNotifier;
    _groupChangedListener.addListener(_onGroupDataChanged);

    _loadGroupData();
  }

  @override
  void dispose() {
    _groupChangedListener.removeListener(_onGroupDataChanged);
    super.dispose();
  }

  /// 数据库变更回调，重新加载群组数据
  void _onGroupDataChanged() {
    if (mounted) {
      _loadGroupData();
    }
  }

  /// 加载群组数据（成员列表、密码等）
  Future<void> _loadGroupData() async {
    try {
      final db = await DatabaseService.instance.database;

      // 加载群组信息
      final groupRows = await db.query(
        'groups',
        where: 'group_id = ?',
        whereArgs: [widget.conversationId],
        limit: 1,
      );

      if (groupRows.isNotEmpty) {
        final group = groupRows.first;
        _password = (group['password'] as int? ?? 0).toString();
        if (_password == '0') _password = '';
      }

      // 加载群成员
      final memberRows = await db.rawQuery(
        '''
        SELECT gm.device_id, gm.member_id, gm.nickname, gm.avatar_index, c.nickname as contact_nickname
        FROM group_members gm
        LEFT JOIN contacts c ON gm.device_id = c.device_id
        WHERE gm.group_id = ?
        ORDER BY gm.joined_at ASC
      ''',
        [widget.conversationId],
      );

      setState(() {
        _members = memberRows.map((row) {
          final deviceId = row['device_id'] as String;
          final memberId = row['member_id'] as int?;
          final isCreator =
              deviceId == DeviceManager.instance.deviceIdNotifier.value;

          // 生成显示名称：使用member_id而不是从device_id提取
          String displayName;
          if (isCreator) {
            // 群主显示自己的昵称或"我"
            final userNickname = UserProfile.instance.nicknameNotifier.value;
            displayName =
                (userNickname != null && userNickname.trim().isNotEmpty)
                ? userNickname.trim()
                : '我';
          } else {
            // 其他用户显示匿名用户格式，使用member_id
            if (memberId != null) {
              displayName = '匿名用户 - $memberId';
            } else {
              // 兼容旧数据，如果没有member_id则使用device_id的最后2位
              if (deviceId.startsWith('0x') && deviceId.length >= 4) {
                final shortHex = deviceId.substring(deviceId.length - 2);
                displayName = '匿名用户 - 0x$shortHex';
              } else {
                displayName = '匿名用户';
              }
            }
          }

          return {
            'device_id': deviceId,
            'nickname': displayName,
            'avatar_index': row['avatar_index'] as int? ?? 0,
            'is_creator': isCreator,
          };
        }).toList();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('[PrivateGroupDetail] 加载群组数据失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(loc.privateGroup_detail_title),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
        ),
        // 移除保存按钮，因为不允许修改
        actions: [],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      // 群成员
                      _buildMembersSection(context, loc),

                      // 聊天信息
                      _buildChatInfoSection(context, loc),

                      // 聊天记录
                      _buildChatHistorySection(context, loc),

                      const SizedBox(height: 40),
                    ],
                  ),
                ),

                // 底部解散群组按钮
                _buildBottomButton(context, loc),
              ],
            ),
    );
  }

  /// 构建群成员部分
  Widget _buildMembersSection(BuildContext context, AppLocalizations loc) {
    return Column(
      children: [
        _buildSectionHeader(
          context,
          '${loc.privateGroup_groupMembers} (${_members.length})',
        ),
        Container(
          color: context.tileBackground,
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          child: _members.isEmpty
              ? Text(
                  '暂无成员',
                  style: TextStyle(
                    color: context.textSecondaryCol,
                    fontSize: 14,
                  ),
                )
              : Wrap(
                  spacing: 16,
                  runSpacing: 16,
                  children: _members
                      .map((member) => _buildMemberItem(context, member))
                      .toList(),
                ),
        ),
      ],
    );
  }

  /// 构建单个成员项
  Widget _buildMemberItem(BuildContext context, Map<String, dynamic> member) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: context.brandPrimary.withValues(alpha: 0.1),
              child: Text(
                member['nickname'].toString().isNotEmpty
                    ? member['nickname'].toString()[0].toUpperCase()
                    : '?',
                style: TextStyle(
                  color: context.brandPrimary,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (member['is_creator'] == true)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: const Icon(Icons.star, size: 10, color: Colors.white),
                ),
              ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          member['nickname'].toString(),
          style: TextStyle(fontSize: 12, color: context.textPrimaryCol),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// 构建聊天信息部分
  Widget _buildChatInfoSection(BuildContext context, AppLocalizations loc) {
    return Column(
      children: [
        _buildSectionHeader(context, loc.privateGroup_chatInfo),
        _buildSettingItem(
          context: context,
          title: loc.privateGroup_groupName,
          trailing: Text(
            _groupName,
            style: TextStyle(color: context.textSecondaryCol, fontSize: 16),
          ),
          onTap: () {}, // 不可编辑
        ),
        _buildSettingItem(
          context: context,
          title: loc.privateGroup_groupQRCode,
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.qr_code, size: 20, color: context.textSecondaryCol),
              const SizedBox(width: 8),
              Icon(Icons.share, size: 16, color: context.textSecondaryCol),
            ],
          ),
          onTap: _showQRCode,
        ),
        _buildSettingItem(
          context: context,
          title: loc.privateGroup_groupChannel,
          trailing: Text(
            _selectedChannel.toString(),
            style: TextStyle(color: context.textSecondaryCol, fontSize: 16),
          ),
          onTap: () {}, // 不可编辑
        ),
        _buildSettingItem(
          context: context,
          title: loc.privateGroup_groupPassword,
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _password.isEmpty
                    ? '未设置'
                    : (_isPasswordVisible ? _password : '****'),
                style: TextStyle(color: context.textSecondaryCol, fontSize: 16),
              ),
              const SizedBox(width: 8),
              Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                size: 16,
                color: context.textSecondaryCol,
              ),
            ],
          ),
          onTap: _togglePasswordVisibility, // 切换密码显示/隐藏
        ),
      ],
    );
  }

  /// 构建聊天记录部分
  Widget _buildChatHistorySection(BuildContext context, AppLocalizations loc) {
    return Column(
      children: [
        _buildSectionHeader(context, loc.privateGroup_chatHistory),
        _buildSettingItem(
          context: context,
          title: loc.privateGroup_searchHistory,
          onTap: () {
            // TODO: 搜索聊天记录
          },
        ),
        _buildSettingItem(
          context: context,
          title: loc.privateGroup_deleteHistory,
          titleColor: Theme.of(context).colorScheme.error,
          onTap: () => _showDeleteConfirm(context, loc),
        ),
      ],
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButton(BuildContext context, AppLocalizations loc) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: context.bgPrimary,
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: ElevatedButton(
          onPressed: () => _showLeaveGroupConfirm(context, loc),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: Theme.of(context).colorScheme.error,
            elevation: 0,
            side: BorderSide(color: Theme.of(context).colorScheme.error),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            loc.privateGroup_leaveGroup,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
        ),
      ),
    );
  }

  /// 切换密码显示/隐藏
  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  /// 显示群二维码
  void _showQRCode() {
    // TODO: 实现二维码显示
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('二维码功能待实现')));
  }

  /// 显示删除确认对话框
  void _showDeleteConfirm(BuildContext context, AppLocalizations loc) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(loc.privateGroup_deleteHistory),
        content: Text('确定要删除所有聊天记录吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx),
            child: Text(loc.privateGroup_cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(ctx);
              try {
                await _deleteGroupHistory();
                if (mounted) {
                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(const SnackBar(content: Text('聊天记录已删除')));
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(SnackBar(content: Text('删除失败: $e')));
                }
              }
            },
            child: Text(
              loc.privateGroup_done,
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示解散群组确认对话框
  void _showLeaveGroupConfirm(BuildContext context, AppLocalizations loc) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(loc.privateGroup_leaveGroup),
        content: Text('确定要解散此群组吗？解散后所有成员将被移除，聊天记录将被删除。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx),
            child: Text(loc.privateGroup_cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(ctx);
              await _disbandGroup();
              if (mounted) Navigator.pop(context, true); // 返回true表示群组已解散
            },
            child: Text(
              loc.privateGroup_leaveGroup,
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  /// 删除群组聊天记录
  Future<void> _deleteGroupHistory() async {
    try {
      final db = await DatabaseService.instance.database;

      // 删除聊天记录
      await db.delete(
        'group_messages',
        where: 'group_id = ?',
        whereArgs: [widget.conversationId],
      );

      // 重置会话信息
      await db.update(
        'group_conversations',
        {'unread_count': 0, 'last_msg_time': 0},
        where: 'conversation_id = ?',
        whereArgs: [widget.conversationId],
      );

      // 通知会话列表刷新
      DatabaseService.groupChangedNotifier.value++;
    } catch (e) {
      debugPrint('[PrivateGroupDetail] 删除聊天记录失败: $e');
      rethrow;
    }
  }

  /// 解散群组
  Future<void> _disbandGroup() async {
    try {
      final db = await DatabaseService.instance.database;

      // 删除群成员
      await db.delete(
        'group_members',
        where: 'group_id = ?',
        whereArgs: [widget.conversationId],
      );

      // 删除聊天记录
      await db.delete(
        'group_messages',
        where: 'group_id = ?',
        whereArgs: [widget.conversationId],
      );

      // 删除会话记录
      await db.delete(
        'group_conversations',
        where: 'conversation_id = ?',
        whereArgs: [widget.conversationId],
      );

      // 删除群组
      await db.delete(
        'groups',
        where: 'group_id = ?',
        whereArgs: [widget.conversationId],
      );

      // 通知数据变更
      DatabaseService.groupChangedNotifier.value++;
    } catch (e) {
      debugPrint('[PrivateGroupDetail] 解散群组失败: $e');
      rethrow;
    }
  }

  // ======= 公共的分组标题和设置项构建 =======
  Widget _buildSectionHeader(BuildContext context, String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 16, top: 12, bottom: 4),
      color: context.bgSecondary,
      child: Text(
        title,
        textAlign: TextAlign.left,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: context.textSecondaryCol,
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required BuildContext context,
    required String title,
    Widget? trailing,
    Color? titleColor,
    required VoidCallback onTap,
  }) {
    return Container(
      color: context.tileBackground,
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(
            color: titleColor ?? context.textPrimaryCol,
            fontSize: 16,
          ),
        ),
        trailing:
            trailing ??
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: context.textSecondaryCol,
            ),
        onTap: onTap,
      ),
    );
  }
}
