import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:gal/gal.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import '../../l10n/app_localizations.dart';
import '../../core/constants/colors.dart';
import '../../core/models/user_qr_data.dart';
import '../../core/services/group_qr_service.dart';

class GroupQrCodeScreen extends StatefulWidget {
  final String groupId;
  final String groupName;

  const GroupQrCodeScreen({
    super.key,
    required this.groupId,
    required this.groupName,
  });

  @override
  State<GroupQrCodeScreen> createState() => _GroupQrCodeScreenState();
}

class _GroupQrCodeScreenState extends State<GroupQrCodeScreen> {
  GroupQrData? _qrData;
  String? _qrString;
  bool _isLoading = true;
  String? _errorMessage;
  final GlobalKey _qrKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _loadQrData();
  }

  Future<void> _loadQrData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final qrData = await GroupQrService.getGroupQrData(widget.groupId);
      final qrString = await GroupQrService.generateGroupQrString(
        widget.groupId,
      );

      setState(() {
        _qrData = qrData;
        _qrString = qrString;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to generate group QR code: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _saveQrCodeImage() async {
    try {
      // 检查权限
      final hasAccess = await Gal.hasAccess();
      if (!hasAccess) {
        final requestGranted = await Gal.requestAccess();
        if (!requestGranted) {
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('需要相册访问权限才能保存图片')));
          }
          return;
        }
      }

      // 截取二维码图片
      final RenderRepaintBoundary boundary =
          _qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      // 保存到相册
      await Gal.putImageBytes(
        pngBytes,
        name: "aitalk_group_qrcode_${DateTime.now().millisecondsSinceEpoch}",
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Group QR code saved to gallery')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Save failed: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(loc.privateGroup_groupQRCode),
        centerTitle: true,
        actions: [
          if (_qrString != null)
            IconButton(
              icon: const Icon(Icons.save_alt),
              onPressed: _saveQrCodeImage,
              tooltip: '保存二维码',
            ),
        ],
      ),
      body: _buildBody(context, loc),
    );
  }

  Widget _buildBody(BuildContext context, AppLocalizations loc) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: context.textSecondaryCol,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: context.textSecondaryCol),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadQrData,
              child: Text(loc.profile_qrcode_retry),
            ),
          ],
        ),
      );
    }

    if (_qrData == null || _qrString == null) {
      return Center(child: Text(loc.profile_qrcode_noData));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // 二维码显示区域
          _buildQrCodeSection(context, loc),
          const SizedBox(height: 32),
          // 群组信息显示区域
          _buildGroupInfoSection(context, loc),
        ],
      ),
    );
  }

  Widget _buildQrCodeSection(BuildContext context, AppLocalizations loc) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: context.bgPrimary,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 二维码
          RepaintBoundary(
            key: _qrKey,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: QrImageView(
                data: _qrString!,
                version: QrVersions.auto,
                size: 200.0,
                backgroundColor: Colors.white,
                eyeStyle: const QrEyeStyle(
                  eyeShape: QrEyeShape.square,
                  color: Colors.black,
                ),
                dataModuleStyle: const QrDataModuleStyle(
                  dataModuleShape: QrDataModuleShape.square,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Scan QR code to join group',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: context.textSecondaryCol),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGroupInfoSection(BuildContext context, AppLocalizations loc) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.bgPrimary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Group Information',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Group Name', _qrData!.groupName),
          _buildInfoRow('Group ID', _qrData!.groupId),
          _buildInfoRow('Channel', _qrData!.channelDisplayText),
          _buildInfoRow('Password', _qrData!.passwordDisplayText),
          _buildInfoRow('Member Count', '${_qrData!.memberCount} members'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: context.textSecondaryCol),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }
}
