import 'package:flutter/material.dart';

import '../../core/constants/colors.dart';
import '../../l10n/app_localizations.dart';
import '../../core/services/database_service.dart';
import '../../core/protocol/tk8620_protocol.dart';
import '../../core/protocol/tk8620_request_sender.dart';
import '../../core/bluetooth/bluetooth_manager.dart';
import '../../core/device/device_manager.dart';
import '../../core/services/conversation_manager.dart';
import '../../core/services/channel_manager.dart';
import '../../core/services/frequency_switcher.dart';
import 'private_group_detail_screen.dart';

// 新增: 通用消息监听器及气泡组件
import 'listeners/chat_message_listener.dart';
import 'widgets/message_bubble.dart';
import 'dart:async';

// 新增: 语音录音和功能面板组件
import '../../core/audio/ptt_recorder.dart';
import 'widgets/chat_action_panel.dart';
import 'ptt_talk_screen.dart';
import '../calls/voice_call_dialing_screen.dart';

/// 私有群会话界面，用于展示私有群聊消息并发送新消息。
/// 与 [PublicGroupChatScreen] 的主要区别：
///   1. 仅显示群真实名称，不带信道切换按钮；
///   2. 不涉及公共群信道切换逻辑。
class PrivateGroupChatScreen extends StatefulWidget {
  /// 群名称，直接显示在标题栏
  final String groupName;

  /// 群对应的会话 ID
  final String conversationId;

  const PrivateGroupChatScreen({
    super.key,
    required this.groupName,
    required this.conversationId,
  });

  @override
  State<PrivateGroupChatScreen> createState() => _PrivateGroupChatScreenState();
}

class _PrivateGroupChatScreenState extends State<PrivateGroupChatScreen> {
  // 当前页面消息列表 (统一使用 ChatMessage)
  final List<ChatMessage> _messages = [];

  // 输入框控制器
  final TextEditingController _textController = TextEditingController();

  // 列表滚动控制器
  final ScrollController _scrollController = ScrollController();

  // 当前信道的 Stream 订阅（留作后续扩展）
  StreamSubscription? _msgSub;

  // 语音状态监听订阅
  StreamSubscription<bool>? _recordingStateSub;

  late String _groupName;
  late String _conversationId;
  // 私有群所属信道 (1-16)，默认 1
  int _channel = 1;

  // 是否处于语音发送模式
  bool _voiceMode = false;
  // 是否正在录音中
  bool _isRecording = false;
  // 是否显示底部功能面板
  bool _showActionPanel = false;

  @override
  void initState() {
    super.initState();
    _groupName = widget.groupName;
    _conversationId = widget.conversationId;

    // 先切换到该私有群频点
    _initAndSwitchFrequency();

    // 进入会话并清零未读数
    debugPrint('🏠 [PrivateGroupChat] 进入私有群会话: $_conversationId');
    ConversationManager.enter(_conversationId);
    debugPrint(
      '🏠 [PrivateGroupChat] 当前激活会话: ${ConversationManager.currentConversationId.value}',
    );
    _clearUnreadCount();

    // 加载历史消息
    _loadMessagesFromDb();

    // 监听实时消息 (文本/语音)
    _msgSub = ChatMessageListener.listen(_conversationId, (msg) {
      if (msg.isVoice) {
        // 语音消息：重新加载整个消息列表以获取正确的语音路径
        _loadMessagesFromDb();
      } else {
        // 文本消息：直接添加到列表
        setState(() {
          _messages.add(msg);
          _scrollToBottom();
        });
      }
    });

    // 监听数据库群组变更（如语音消息保存完成）
    DatabaseService.groupChangedNotifier.addListener(_onGroupChanged);
  }

  // 滚动到底部
  void _scrollToBottom() {
    if (!_scrollController.hasClients) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
  }

  void _onGroupChanged() {
    // 当数据库群组变更时（如语音消息保存完成），重新加载消息
    _loadMessagesFromDb();
  }

  @override
  void dispose() {
    // 离开会话，确保未读计数生效
    ConversationManager.exit();

    _msgSub?.cancel();
    _recordingStateSub?.cancel();
    _textController.dispose();
    _scrollController.dispose();

    // 移除数据库变更监听
    DatabaseService.groupChangedNotifier.removeListener(_onGroupChanged);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.bgPrimary,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: context.bgPrimary,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          color: context.brandPrimary,
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          _groupName,
          style: TextStyle(
            color: context.textPrimaryCol,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Image.asset(
              'assets/images/more_icon.png',
              width: 24,
              height: 24,
              color: context.textPrimaryCol,
            ),
            onPressed: _showGroupDetail,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final msg = _messages[index];
                return MessageBubble(message: msg);
              },
            ),
          ),
          Divider(height: 1, color: context.bgSecondary.withValues(alpha: 0.5)),
          _buildInputBar(),
          // 底部加号展开的功能面板
          ChatActionPanel(
            visible: _showActionPanel,
            onPttPressed: () {
              setState(() {
                _showActionPanel = false;
              });
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => PttTalkScreen(
                    conversationId: _conversationId,
                    groupName: _groupName,
                  ),
                ),
              );
            },
            onVoiceCallPressed: () {
              setState(() {
                _showActionPanel = false;
              });
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => VoiceCallDialingScreen(
                    conversationId: _conversationId,
                    groupName: _groupName,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// 构建底部输入栏
  Widget _buildInputBar() {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            IconButton(
              icon: Icon(Icons.mic, color: context.brandPrimary),
              onPressed: () {
                setState(() {
                  _voiceMode = !_voiceMode;
                });
              },
            ),
            Expanded(
              child: _voiceMode
                  ? GestureDetector(
                      onLongPressStart: (_) {
                        _startRecording();
                      },
                      onLongPressEnd: (_) {
                        _stopRecording();
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 40,
                        decoration: BoxDecoration(
                          color: context.bgSecondary,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          _isRecording
                              ? AppLocalizations.of(
                                  context,
                                )!.publicChat_recording
                              : AppLocalizations.of(
                                  context,
                                )!.publicChat_pressHold,
                          style: TextStyle(color: context.textPrimaryCol),
                        ),
                      ),
                    )
                  : Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        color: context.bgSecondary,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: TextField(
                        controller: _textController,
                        textInputAction: TextInputAction.send,
                        onSubmitted: (_) => _handleSend(),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: AppLocalizations.of(
                            context,
                          )!.publicChat_inputHint,
                        ),
                      ),
                    ),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: Icon(
                Icons.add_circle_outline,
                color: context.brandPrimary,
                size: 28,
              ),
              onPressed: () {
                setState(() {
                  _showActionPanel = !_showActionPanel;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 从数据库加载历史消息
  Future<void> _loadMessagesFromDb() async {
    final db = await DatabaseService.instance.database;

    // 联合查询获取member_id信息
    final rows = await db.rawQuery(
      '''
      SELECT gm.*, gme.member_id
      FROM group_messages gm
      LEFT JOIN group_members gme ON gme.group_id = gm.group_id
        AND gme.device_id = printf('0x%08X', gm.src_id)
      WHERE gm.group_id = ? AND gm.message_type IN (?, ?)
      ORDER BY gm.created_at ASC
      ''',
      [_conversationId, TK8620DataType.text, TK8620DataType.voice],
    );

    final List<ChatMessage> history = rows.map((row) {
      final createdAt = DateTime.fromMillisecondsSinceEpoch(
        row['created_at'] as int,
      );
      final int type = row['message_type'] as int;
      final memberId = row['member_id'] as int?;

      if (type == TK8620DataType.voice) {
        return ChatMessage(
          srcId: row['src_id'] as int,
          content: '',
          isMine: (row['is_mine'] as int) == 1,
          time: _formatTime(createdAt),
          isVoice: true,
          voicePath: row['content'] as String,
          groupId: _conversationId,
          memberId: memberId,
        );
      } else {
        return ChatMessage(
          srcId: row['src_id'] as int, // 从数据库读取实际的srcId
          content: row['content'] as String,
          isMine: (row['is_mine'] as int) == 1,
          time: _formatTime(createdAt),
          groupId: _conversationId,
          memberId: memberId,
        );
      }
    }).toList();

    setState(() {
      _messages
        ..clear()
        ..addAll(history);
      _scrollToBottom();
    });
  }

  /// 清零未读计数
  Future<void> _clearUnreadCount() async {
    final db = await DatabaseService.instance.database;
    await db.update(
      'group_conversations',
      {'unread_count': 0},
      where: 'conversation_id = ?',
      whereArgs: [_conversationId],
    );
    DatabaseService.groupChangedNotifier.value++;
  }

  /// 格式化时间为 HH:mm
  String _formatTime(DateTime dt) {
    final h = dt.hour.toString().padLeft(2, '0');
    final m = dt.minute.toString().padLeft(2, '0');
    return '$h:$m';
  }

  /// 发送文本消息
  void _handleSend() async {
    final text = _textController.text.trim();
    if (text.isEmpty) return;

    // 1️⃣ 解析目标地址 (dstId) —— 若失败直接放弃发送
    int dstId;
    try {
      String idStr = _conversationId;
      if (idStr.startsWith('0x') || idStr.startsWith('0X')) {
        idStr = idStr.substring(2);
      }
      dstId = int.parse(idStr, radix: 16);
    } catch (_) {
      debugPrint('❌ 解析私有群ID失败，取消发送');
      return;
    }

    // 2️⃣ 清空输入框
    _textController.clear();

    // 3️⃣ 本地加入消息列表并刷新UI
    final msg = ChatMessage(
      srcId: 0,
      content: text,
      isMine: true,
      time: _formatTime(DateTime.now()),
      groupId: _conversationId,
      memberId: 0, // 群主的memberId为0
    );
    setState(() {
      _messages.add(msg);
      _scrollToBottom();
    });

    // 4️⃣ 获取当前已连接设备
    final device = BluetoothManager.currentDevice.value;
    if (device == null) {
      debugPrint('⚠️ 未连接设备，无法发送文本消息');
      return;
    }

    // 5️⃣ 获取当前设备的群内成员ID
    int? srcId;
    final deviceIdHex = DeviceManager.instance.deviceIdNotifier.value;
    if (deviceIdHex != null && deviceIdHex.startsWith('0x')) {
      try {
        final fullDeviceId = int.parse(deviceIdHex.substring(2), radix: 16);
        // 在私有群中，必须使用群内成员ID
        final memberId = await DatabaseService.instance.getMemberIdByDeviceId(
          _conversationId,
          fullDeviceId,
        );
        if (memberId != null) {
          srcId = memberId;
          debugPrint('🔄 使用群内成员ID作为srcId: $srcId (设备ID: $deviceIdHex)');
        } else {
          debugPrint('❌ 未找到群内成员ID，无法发送消息 (设备ID: $deviceIdHex)');
          return; // 不发送消息
        }
      } catch (e) {
        debugPrint('❌ 解析设备ID失败: $e');
        return; // 不发送消息
      }
    } else {
      debugPrint('❌ 无效的设备ID，无法发送消息');
      return; // 不发送消息
    }

    try {
      await TK8620RequestSender.sendTextMessage(
        device,
        text: text,
        srcId: srcId,
        dstId: dstId,
      );

      // 6️⃣ 发送成功后保存到数据库
      _saveMessageToDb(
        content: text,
        isMine: true,
        srcId: srcId,
        messageType: TK8620DataType.text,
      );
    } catch (e) {
      debugPrint('❌ 发送文本消息失败: $e');
    }
  }

  /// 保存消息到数据库
  Future<void> _saveMessageToDb({
    required String content,
    required bool isMine,
    required int srcId,
    required int messageType,
  }) async {
    final db = await DatabaseService.instance.database;
    final nowMs = DateTime.now().millisecondsSinceEpoch;

    await db.insert('group_messages', {
      'group_id': _conversationId,
      'src_id': srcId,
      'message_type': messageType,
      'content': content,
      'is_mine': isMine ? 1 : 0,
      'created_at': nowMs,
    });

    // 更新 last_msg_time
    await db.update(
      'group_conversations',
      {'last_msg_time': nowMs ~/ 1000},
      where: 'conversation_id = ?',
      whereArgs: [_conversationId],
    );
  }

  /// 根据 groups 表中记录的 channel 字段，切换到对应私有频点。
  Future<void> _initAndSwitchFrequency() async {
    try {
      final db = await DatabaseService.instance.database;
      final rows = await db.query(
        'groups',
        columns: ['channel'],
        where: 'group_id = ?',
        whereArgs: [_conversationId],
        limit: 1,
      );
      if (rows.isNotEmpty && rows.first['channel'] != null) {
        _channel = rows.first['channel'] as int;
      }
    } catch (_) {
      // 若查询失败则保持默认 1
    }

    // 更新全局当前信道
    ChannelManager.setChannel(_channel);
    // 发送切换私有群频点指令
    await FrequencySwitcher.switchPrivate(_channel);
  }

  // 开始录音
  void _startRecording() async {
    if (_isRecording) return;
    debugPrint('🎤 [PrivateGroupChat] 开始录音，当前会话: $_conversationId');
    debugPrint(
      '🎤 [PrivateGroupChat] ConversationManager当前会话: ${ConversationManager.currentConversationId.value}',
    );
    await PttRecorder.instance.start();
    setState(() {
      _isRecording = true;
    });
  }

  // 停止录音并处理语音消息
  void _stopRecording() async {
    if (!_isRecording) return;
    setState(() {
      _isRecording = false;
    });
    debugPrint('🎤 [PrivateGroupChat] 停止录音，当前会话: $_conversationId');
    debugPrint(
      '🎤 [PrivateGroupChat] ConversationManager当前会话: ${ConversationManager.currentConversationId.value}',
    );
    await PttRecorder.instance.stop();
    debugPrint('✅ 实时录音结束');
  }

  /// 显示群组详情页面
  void _showGroupDetail() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PrivateGroupDetailScreen(
          conversationId: _conversationId,
          groupName: _groupName,
          initialChannel: _channel,
        ),
      ),
    );

    // 如果群组信息有更新，刷新当前页面
    if (result != null && result is Map<String, dynamic>) {
      setState(() {
        _groupName = result['groupName'] ?? _groupName;
        _channel = result['channel'] ?? _channel;
      });
    } else if (result == true) {
      // 群组已解散，返回上一页
      if (mounted) Navigator.pop(context);
    }
  }
}
