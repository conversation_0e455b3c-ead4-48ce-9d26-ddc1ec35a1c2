import 'package:flutter/material.dart';
import 'package:characters/characters.dart';
import '../../l10n/app_localizations.dart';

import '../../core/constants/colors.dart'; // 导入颜色常量
import 'settings_screen.dart'; // 导入设置页面
import '../../core/device/device_manager.dart';
import '../../core/user/user_profile.dart';
import 'profile_detail_screen.dart'; // 导入个人资料页面
import '../device/device_management_screen.dart'; // 导入设备管理页面
import 'qr_code_screen.dart'; // 导入二维码页面

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context)!;

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 1. 资料显示栏 - 增加上下内边距
            _buildProfileHeader(context, loc),

            // 添加灰色间隔
            Container(
              height: 12,
              width: double.infinity,
              color: context.bgSecondary,
            ),

            // 2. 选择列表
            _buildListSection(context, loc),

            const SizedBox(height: 16),

            // 3. 设置
            _buildSettingsItem(context, loc),

            // 填充空白区域
            Expanded(
              child: SizedBox(
                width: double.infinity,
                child: DecoratedBox(
                  decoration: BoxDecoration(color: context.bgSecondary),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 资料显示栏
  Widget _buildProfileHeader(BuildContext context, AppLocalizations loc) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: 24,
        vertical: 56,
      ), // 进一步增加上下内边距
      color: context.bgPrimary,
      child: Row(
        children: [
          // 用户头像 - 根据昵称首字符动态显示
          ValueListenableBuilder<String?>(
            valueListenable: UserProfile.instance.nicknameNotifier,
            builder: (context, nickname, _) {
              final String firstChar = (nickname?.trim().isNotEmpty ?? false)
                  ? nickname!.trim().characters.first
                  : '?';
              return CircleAvatar(
                radius: 36,
                backgroundColor: context.brandPrimary,
                child: Text(
                  firstChar,
                  style: const TextStyle(
                    fontSize: 36,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 20), // 恢复原来间距
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ValueListenableBuilder<String?>(
                  valueListenable: UserProfile.instance.nicknameNotifier,
                  builder: (context, nickname, _) {
                    final displayName = (nickname?.trim() ?? '');
                    return Text(
                      displayName,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 8), // 恢复原来间距
                // 设备 ID 通过 DeviceManager 缓存并监听
                ValueListenableBuilder<String?>(
                  valueListenable: DeviceManager.instance.deviceIdNotifier,
                  builder: (context, id, _) {
                    String idText;
                    if (id == null) {
                      idText = loc.deviceInfo_deviceId_loading;
                    } else if (id == 'FAILED') {
                      idText = loc.deviceInfo_deviceId_failed;
                    } else {
                      idText = id;
                    }
                    return Text(
                      loc.profile_deviceId(idText),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: context.textSecondaryCol,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          // QR码图标和分享图标
          Row(
            children: [
              IconButton(
                icon: Icon(
                  Icons.qr_code_2,
                  size: 28,
                  color: context.brandPrimary,
                ),
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(builder: (_) => const QrCodeScreen()),
                  );
                },
              ),
              IconButton(
                icon: Icon(
                  Icons.chevron_right,
                  size: 28,
                  color: context.textSecondaryCol,
                ),
                onPressed: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (_) => const ProfileDetailScreen(),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 选择列表部分
  Widget _buildListSection(BuildContext context, AppLocalizations loc) {
    return Container(
      color: context.bgPrimary,
      child: Column(
        children: [
          // 设备管理
          _buildListItem(
            context: context,
            icon: Icons.devices,
            iconColor: context.brandPrimary,
            title: loc.profile_deviceManagement,
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const DeviceManagementScreen()),
            ),
          ),
          _buildDivider(context),

          // 用户指南
          _buildListItem(
            context: context,
            icon: Icons.menu_book,
            iconColor: context.brandPrimary,
            title: loc.profile_userGuide,
            onTap: () {},
          ),
          _buildDivider(context),

          // 开发者模式
          _buildListItem(
            context: context,
            icon: Icons.developer_mode,
            iconColor: context.brandPrimary,
            title: loc.profile_developerMode,
            onTap: () {},
          ),
        ],
      ),
    );
  }

  // 设置项
  Widget _buildSettingsItem(BuildContext context, AppLocalizations loc) {
    return Container(
      color: context.bgPrimary,
      child: _buildListItem(
        context: context,
        icon: Icons.settings,
        iconColor: context.brandPrimary,
        title: loc.profile_settings,
        onTap: () => Navigator.of(
          context,
        ).push(MaterialPageRoute(builder: (context) => const SettingsScreen())),
      ),
    );
  }

  // 列表项通用模板
  Widget _buildListItem({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 4,
      ), // 减小内边距
      leading: Container(
        padding: const EdgeInsets.all(10), // 稍微大一点的内边距
        decoration: BoxDecoration(
          color: iconColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8), // 恢复原来大小
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: 28, // 稍微大一点的图标
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium, // 恢复原来字号
      ),
      trailing: Icon(
        Icons.chevron_right,
        color: context.textSecondaryCol,
        size: 24,
      ), // 调整大小
      onTap: onTap,
    );
  }

  // 分隔线
  Widget _buildDivider(BuildContext context) {
    return Divider(
      height: 1,
      thickness: 1,
      indent: 16,
      endIndent: 16,
      color: context.bgSecondary.withValues(alpha: 0.5),
    );
  }
}
