import 'dart:convert';

/// 群组二维码数据模型
/// 包含群组的完整信息，用于生成群组邀请二维码
class GroupQrData {
  /// 群组ID
  final String groupId;

  /// 群组名称
  final String groupName;

  /// 群组密码
  final int password;

  /// 群组信道
  final int channel;

  /// 群组成员列表（memberId -> deviceId的映射）
  final Map<int, String> members;

  const GroupQrData({
    required this.groupId,
    required this.groupName,
    required this.password,
    required this.channel,
    required this.members,
  });

  /// 转换为编码后的字符串，用于生成二维码
  /// 使用Base64编码隐藏明文信息
  String toQrString() {
    final data = {
      'type': 'group', // 标识这是群组二维码
      'groupId': groupId,
      'groupName': groupName,
      'password': password,
      'channel': channel,
      'members': members.map((k, v) => MapEntry(k.toString(), v)),
      'version': 1, // 协议版本号，便于后续扩展
    };
    final jsonString = jsonEncode(data);
    // 添加aiTalk标识前缀，然后Base64编码
    final prefixedData = 'aiTalk:$jsonString';
    final encodedBytes = utf8.encode(prefixedData);
    return base64Encode(encodedBytes);
  }

  /// 从编码后的字符串创建实例
  /// 解码Base64并解析JSON数据
  static GroupQrData? fromQrString(String qrString) {
    try {
      // Base64解码
      final decodedBytes = base64Decode(qrString);
      final decodedString = utf8.decode(decodedBytes);

      // 检查aiTalk前缀
      if (!decodedString.startsWith('aiTalk:')) {
        return null; // 不是aiTalk的二维码
      }

      // 移除前缀并解析JSON
      final jsonString = decodedString.substring(7); // 移除'aiTalk:'
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      // 检查是否为群组二维码
      if (data['type'] != 'group') {
        return null; // 不是群组二维码
      }

      // 解析成员列表
      final membersData = data['members'] as Map<String, dynamic>;
      final members = <int, String>{};
      membersData.forEach((key, value) {
        final memberId = int.tryParse(key);
        if (memberId != null) {
          members[memberId] = value as String;
        }
      });

      return GroupQrData(
        groupId: data['groupId'] as String,
        groupName: data['groupName'] as String,
        password: data['password'] as int,
        channel: data['channel'] as int,
        members: members,
      );
    } catch (e) {
      return null; // 解码失败或格式错误
    }
  }

  /// 获取信道显示文本
  String get channelDisplayText {
    return 'Channel $channel';
  }

  /// 获取密码显示文本
  String get passwordDisplayText {
    return password == 0 ? 'No Password' : password.toString();
  }

  /// 获取成员数量
  int get memberCount => members.length;

  @override
  String toString() {
    return 'GroupQrData(groupId: $groupId, groupName: $groupName, password: $password, channel: $channel, memberCount: $memberCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroupQrData &&
        other.groupId == groupId &&
        other.groupName == groupName &&
        other.password == password &&
        other.channel == channel &&
        _mapEquals(other.members, members);
  }

  @override
  int get hashCode {
    return Object.hash(
      groupId,
      groupName,
      password,
      channel,
      Object.hashAll(members.entries.map((e) => Object.hash(e.key, e.value))),
    );
  }

  /// 比较两个Map是否相等
  bool _mapEquals(Map<int, String> map1, Map<int, String> map2) {
    if (map1.length != map2.length) return false;
    for (final key in map1.keys) {
      if (map1[key] != map2[key]) return false;
    }
    return true;
  }
}

/// 用户二维码数据模型
/// 包含用户的频点和速率信息
class UserQrData {
  /// 当前用户所在频点 (Hz)
  final int frequency;

  /// 当前用户使用的速率模式
  final int rateMode;

  /// 用户昵称
  final String nickname;

  /// 设备ID
  final String deviceId;

  const UserQrData({
    required this.frequency,
    required this.rateMode,
    required this.nickname,
    required this.deviceId,
  });

  /// 转换为编码后的字符串，用于生成二维码
  /// 使用Base64编码隐藏明文信息
  String toQrString() {
    final data = {
      'freq': frequency,
      'rate': rateMode,
      'name': nickname,
      'device': deviceId,
      'version': 1, // 协议版本号，便于后续扩展
    };
    final jsonString = jsonEncode(data);
    // 添加aiTalk标识前缀，然后Base64编码
    final prefixedData = 'aiTalk:$jsonString';
    final encodedBytes = utf8.encode(prefixedData);
    return base64Encode(encodedBytes);
  }

  /// 从编码后的字符串创建实例
  /// 解码Base64并解析JSON数据
  static UserQrData? fromQrString(String qrString) {
    try {
      // Base64解码
      final decodedBytes = base64Decode(qrString);
      final decodedString = utf8.decode(decodedBytes);

      // 检查aiTalk前缀
      if (!decodedString.startsWith('aiTalk:')) {
        return null; // 不是aiTalk的二维码
      }

      // 移除前缀并解析JSON
      final jsonString = decodedString.substring(7); // 移除'aiTalk:'
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      return UserQrData(
        frequency: data['freq'] as int,
        rateMode: data['rate'] as int,
        nickname: data['name'] as String,
        deviceId: data['device'] as String,
      );
    } catch (e) {
      return null; // 解码失败或格式错误
    }
  }

  /// 获取频点显示文本 (MHz)
  String get frequencyDisplayText {
    return '${(frequency / 1000000).toStringAsFixed(2)} MHz';
  }

  /// 获取速率模式显示文本
  String get rateModeDisplayText {
    return '速率模式 $rateMode';
  }

  @override
  String toString() {
    return 'UserQrData(frequency: $frequency, rateMode: $rateMode, nickname: $nickname, deviceId: $deviceId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserQrData &&
        other.frequency == frequency &&
        other.rateMode == rateMode &&
        other.nickname == nickname &&
        other.deviceId == deviceId;
  }

  @override
  int get hashCode {
    return Object.hash(frequency, rateMode, nickname, deviceId);
  }
}
