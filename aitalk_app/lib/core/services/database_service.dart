import 'dart:io';

import 'package:flutter/foundation.dart'
    show kDebugMode, debugPrint, ValueNotifier;
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import '../utils/group_util.dart';

/// 单例数据库服务。
/// 负责打开本地 SQLite 数据库并在首次创建时初始化表结构。
class DatabaseService {
  // 数据库文件名
  static const String _dbName = 'aitalk_manager.db';

  // 若未来需要数据库迁移，可在此处递增版本号并在 [onUpgrade] 中处理
  // v1：groups 表新增 is_private 字段
  // v2：新增 bluetooth_devices 表
  // v3：group_members 表新增 member_id 字段
  static const int _dbVersion = 3;

  DatabaseService._();

  static final DatabaseService instance = DatabaseService._();

  Database? _database;

  /// 群组变更通知（新增/删除等）
  static final ValueNotifier<int> groupChangedNotifier = ValueNotifier<int>(0);

  /// 设备列表变更通知（新增/更新时间）
  static final ValueNotifier<int> deviceListChanged = ValueNotifier<int>(0);

  /// 获取数据库实例（懒加载）。
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    // 启动后保证公共群完整
    await _ensurePublicGroups(_database!);
    // Debug 打印所有群组
    if (kDebugMode) {
      await _logAllGroups(_database!);
    }
    return _database!;
  }

  // ---------------------------- private ----------------------------

  Future<Database> _initDatabase() async {
    // Documents 目录在 iOS/Android 都可写，path_provider 会自动返回正确路径
    final Directory docsDir = await getApplicationDocumentsDirectory();
    final String dbPath = join(docsDir.path, _dbName);

    // 调试模式下总是删除旧库以便快速迭代表结构；发布版将跳过此步骤
    if (kDebugMode) {
      debugPrint(
        '[DatabaseService] Debug mode: deleting existing database at $dbPath',
      );
      await deleteDatabase(dbPath);
    }

    return openDatabase(
      dbPath,
      version: _dbVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// 首次创建数据库时回调：建表
  Future<void> _onCreate(Database db, int version) async {
    // 1. 联系人（用户）表
    await db.execute('''
      CREATE TABLE contacts (
        device_id     TEXT PRIMARY KEY,        -- 设备唯一ID，用于识别用户
        nickname      TEXT NOT NULL,           -- 用户昵称
        avatar_index  INTEGER NOT NULL DEFAULT 0, -- 头像索引（本地资源下标）
        created_at    INTEGER NOT NULL,        -- 创建时间（时间戳，毫秒）
        updated_at    INTEGER NOT NULL         -- 更新时间（时间戳，毫秒）
      );
    ''');

    // 2. 群组表
    await db.execute('''
      CREATE TABLE groups (
        group_id    TEXT PRIMARY KEY,          -- 群组唯一ID
        group_name  TEXT NOT NULL,             -- 群名称
        channel     INTEGER NOT NULL DEFAULT 0, -- 信道编号（0-15）
        is_private  INTEGER NOT NULL DEFAULT 0, -- 是否私有群 (0 否 / 1 是)
        password    INTEGER NOT NULL DEFAULT 0, -- 群组密码（int，0表示无密码）
        creator_id  TEXT NOT NULL,             -- 创建人device_id
        created_at  INTEGER NOT NULL,          -- 创建时间
        updated_at  INTEGER NOT NULL,          -- 更新时间
        FOREIGN KEY (creator_id) REFERENCES contacts(device_id)
      );
    ''');

    // 3. 群组成员表（多对多）
    await db.execute('''
      CREATE TABLE group_members (
        group_id      TEXT NOT NULL,           -- 所属群组ID
        device_id     TEXT NOT NULL,           -- 成员device_id
        member_id     INTEGER,                 -- 群内成员ID（1字节，用于显示）
        nickname      TEXT,                    -- 群内昵称（可覆盖全局昵称）
        avatar_index  INTEGER,                 -- 群内头像索引（可覆盖全局头像）
        joined_at     INTEGER NOT NULL,        -- 加入时间
        PRIMARY KEY (group_id, device_id),
        FOREIGN KEY (group_id)  REFERENCES groups(group_id)   ON DELETE CASCADE,
        FOREIGN KEY (device_id) REFERENCES contacts(device_id) ON DELETE CASCADE
      );
    ''');

    // 4. 群组会话表
    await db.execute('''
      CREATE TABLE group_conversations (
        conversation_id TEXT PRIMARY KEY,      -- 会话ID（可与group_id一致）
        group_id        TEXT NOT NULL,         -- 所属群组ID
        unread_count    INTEGER NOT NULL DEFAULT 0, -- 未读消息数
        last_msg_time   INTEGER,               -- 最后一条消息时间（时间戳，秒）
        FOREIGN KEY (group_id) REFERENCES groups(group_id)
      );
    ''');

    // 5. 群组消息表
    await db.execute('''
      CREATE TABLE group_messages (
        id            INTEGER PRIMARY KEY AUTOINCREMENT, -- 自增ID
        group_id      TEXT NOT NULL,                    -- 所属群组ID
        src_id        INTEGER NOT NULL,                 -- 发送方 SrcID (短地址)
        message_type  INTEGER NOT NULL DEFAULT 0,       -- 消息类型 0文本/2语音/3位置等 (参考TK8620DataType)
        content       TEXT,                             -- 文本内容或其他载荷
        is_mine       INTEGER NOT NULL DEFAULT 0,       -- 是否本人发送 (0/1)
        created_at    INTEGER NOT NULL,                 -- 创建时间（时间戳，毫秒）
        FOREIGN KEY (group_id) REFERENCES groups(group_id)
      );
    ''');

    // 6. 已连接蓝牙设备表
    await db.execute('''
      CREATE TABLE bluetooth_devices (
        device_id       TEXT PRIMARY KEY,       -- 设备唯一ID (MAC / UUID)
        name            TEXT NOT NULL,          -- 设备名称
        last_connected  INTEGER NOT NULL,       -- 最近连接时间（时间戳，毫秒）
        created_at      INTEGER NOT NULL        -- 首次记录时间（时间戳，毫秒）
      );
    ''');

    // 预置 16 个公共群（channel 1-16，非私有）
    for (int ch = 1; ch <= 16; ch++) {
      final String gid = GroupUtil.publicGroupId(ch);
      await db.insert('groups', {
        'group_id': gid,
        'group_name': '公共群 - $ch', // 显示名称
        'channel': ch,
        'is_private': 0,
        'creator_id': 'system',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });
    }
  }

  /// 数据库版本升级
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // v2：新增 channel 字段
      await db.execute(
        'ALTER TABLE groups ADD COLUMN channel INTEGER NOT NULL DEFAULT 0;',
      );
    }

    if (oldVersion < 3) {
      // v3：新增 is_private 字段
      await db.execute(
        'ALTER TABLE groups ADD COLUMN is_private INTEGER NOT NULL DEFAULT 0;',
      );
    }

    if (oldVersion < 4) {
      // v4：新增 password 字段
      await db.execute(
        'ALTER TABLE groups ADD COLUMN password INTEGER NOT NULL DEFAULT 0;',
      );
    }

    if (oldVersion < 2) {
      // v2：新增 bluetooth_devices 表
      await db.execute('''
        CREATE TABLE bluetooth_devices (
          device_id       TEXT PRIMARY KEY,
          name            TEXT NOT NULL,
          last_connected  INTEGER NOT NULL,
          created_at      INTEGER NOT NULL
        );
      ''');
    }

    if (oldVersion < 3) {
      // v3：group_members 表新增 member_id 字段
      await db.execute(
        'ALTER TABLE group_members ADD COLUMN member_id INTEGER;',
      );

      // 修复已存在的私有群数据：为群主设置member_id=0
      await _fixExistingPrivateGroupCreators(db);
    }
  }

  /// 确保 16 个公共群（信道 1-16）存在，不足则补齐
  Future<void> _ensurePublicGroups(Database db) async {
    // 统计已存在的公共群数量（按命名规则识别）
    final countRes = await db.rawQuery(
      "SELECT COUNT(*) AS cnt FROM groups WHERE group_id LIKE '100000%' AND is_private = 0",
    );
    final int existing = Sqflite.firstIntValue(countRes) ?? 0;
    if (existing >= 16) return;

    final int now = DateTime.now().millisecondsSinceEpoch;

    // 确保系统联系人存在
    await db.insert('contacts', {
      'device_id': 'system',
      'nickname': '系统',
      'avatar_index': 0,
      'created_at': now,
      'updated_at': now,
    }, conflictAlgorithm: ConflictAlgorithm.ignore);

    // 逐信道补齐缺失的公共群
    for (int ch = 1; ch <= 16; ch++) {
      final gid = GroupUtil.publicGroupId(ch);
      await db.insert('groups', {
        'group_id': gid,
        'group_name': '公共群 - $ch',
        'channel': ch,
        'is_private': 0,
        'creator_id': 'system',
        'created_at': now,
        'updated_at': now,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
    }
  }

  /// 打印当前 groups 表全部内容
  Future<void> _logAllGroups(Database db) async {
    final rows = await db.query('groups');
    debugPrint('[DatabaseService] 当前 groups 表共有 ${rows.length} 条记录');
  }

  /// 修复已存在的私有群数据：为群主设置member_id=0
  Future<void> _fixExistingPrivateGroupCreators(Database db) async {
    try {
      // 查找所有私有群
      final privateGroups = await db.query(
        'groups',
        columns: ['group_id', 'creator_id'],
        where: 'is_private = 1',
      );

      for (final group in privateGroups) {
        final groupId = group['group_id'] as String;
        final creatorId = group['creator_id'] as String;

        // 检查群主是否已有member_id
        final existingMember = await db.query(
          'group_members',
          columns: ['member_id'],
          where: 'group_id = ? AND device_id = ?',
          whereArgs: [groupId, creatorId],
          limit: 1,
        );

        if (existingMember.isNotEmpty) {
          final memberId = existingMember.first['member_id'] as int?;
          if (memberId == null) {
            // 群主存在但没有member_id，设置为0
            await db.update(
              'group_members',
              {'member_id': 0},
              where: 'group_id = ? AND device_id = ?',
              whereArgs: [groupId, creatorId],
            );
            debugPrint(
              '[DatabaseService] 修复群主member_id: $groupId -> $creatorId = 0',
            );
          }
        } else {
          // 群主不在成员列表中，添加记录
          await db.insert('group_members', {
            'group_id': groupId,
            'device_id': creatorId,
            'member_id': 0,
            'joined_at': DateTime.now().millisecondsSinceEpoch,
          }, conflictAlgorithm: ConflictAlgorithm.ignore);
          debugPrint('[DatabaseService] 添加群主成员记录: $groupId -> $creatorId = 0');
        }
      }
    } catch (e) {
      debugPrint('[DatabaseService] 修复私有群数据失败: $e');
    }
  }

  /// 确保指定群组 [groupId] 的成员列表中包含 [srcId] 对应的用户。
  /// 若 `contacts` 或 `group_members` 中缺少数据，则自动插入。
  /// [memberId] 是可选的群内成员ID（1字节），用于显示
  Future<void> ensureGroupMember(
    String groupId,
    int srcId, {
    int? memberId,
  }) async {
    final db = await database;

    final String deviceId =
        '0x${srcId.toRadixString(16).toUpperCase().padLeft(8, '0')}';
    final int nowMs = DateTime.now().millisecondsSinceEpoch;

    // 1. contacts 表
    final contact = await db.query(
      'contacts',
      columns: ['device_id'],
      where: 'device_id = ?',
      whereArgs: [deviceId],
      limit: 1,
    );
    if (contact.isEmpty) {
      await db.insert('contacts', {
        'device_id': deviceId,
        'nickname': '匿名用户 - $deviceId',
        'avatar_index': 0,
        'created_at': nowMs,
        'updated_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
      debugPrint('[DatabaseService] 新增联系人 $deviceId');
    }

    // 2. group_members 表
    final member = await db.query(
      'group_members',
      columns: ['device_id'],
      where: 'group_id = ? AND device_id = ?',
      whereArgs: [groupId, deviceId],
      limit: 1,
    );
    if (member.isEmpty) {
      await db.insert('group_members', {
        'group_id': groupId,
        'device_id': deviceId,
        'member_id': memberId, // 保存群内成员ID
        'joined_at': nowMs,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
      debugPrint(
        '[DatabaseService] $deviceId (memberId: $memberId) 加入群组 $groupId',
      );
    }
  }

  /// 记录或更新一次蓝牙设备连接历史。
  /// 每次成功连接后调用，更新 `last_connected` 时间；若不存在则插入。
  Future<void> upsertBluetoothDevice({
    required String deviceId,
    required String name,
  }) async {
    final db = await database;
    final int now = DateTime.now().millisecondsSinceEpoch;

    // 使用 INSERT OR IGNORE 然后 UPDATE, 以便保留最早的 created_at
    await db.insert('bluetooth_devices', {
      'device_id': deviceId,
      'name': name,
      'last_connected': now,
      'created_at': now,
    }, conflictAlgorithm: ConflictAlgorithm.ignore);

    await db.update(
      'bluetooth_devices',
      {'name': name, 'last_connected': now},
      where: 'device_id = ?',
      whereArgs: [deviceId],
    );

    // 通知监听者列表已变化
    deviceListChanged.value++;
  }

  /// 按最近连接时间倒序读取所有已保存的蓝牙设备
  Future<List<Map<String, dynamic>>> fetchBluetoothDevices() async {
    final db = await database;
    return db.query('bluetooth_devices', orderBy: 'last_connected DESC');
  }

  /// 根据设备ID查找群内成员ID
  /// 返回null表示该设备不在群组中或没有分配member_id
  Future<int?> getMemberIdByDeviceId(String groupId, int srcId) async {
    final db = await database;
    final String deviceId =
        '0x${srcId.toRadixString(16).toUpperCase().padLeft(8, '0')}';

    final result = await db.query(
      'group_members',
      columns: ['member_id'],
      where: 'group_id = ? AND device_id = ?',
      whereArgs: [groupId, deviceId],
      limit: 1,
    );

    if (result.isNotEmpty) {
      return result.first['member_id'] as int?;
    }
    return null;
  }

  /// 根据群内成员ID查找设备ID
  /// 返回null表示该member_id不存在
  Future<int?> getDeviceIdByMemberId(String groupId, int memberId) async {
    final db = await database;

    final result = await db.query(
      'group_members',
      columns: ['device_id'],
      where: 'group_id = ? AND member_id = ?',
      whereArgs: [groupId, memberId],
      limit: 1,
    );

    if (result.isNotEmpty) {
      final deviceIdStr = result.first['device_id'] as String;
      try {
        // 移除0x前缀并转换为int
        final idStr = deviceIdStr.startsWith('0x')
            ? deviceIdStr.substring(2)
            : deviceIdStr;
        return int.parse(idStr, radix: 16);
      } catch (e) {
        debugPrint('解析设备ID失败: $deviceIdStr, 错误: $e');
        return null;
      }
    }
    return null;
  }
}
