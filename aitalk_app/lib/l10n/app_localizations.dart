import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// No description provided for @autoReconnect_cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get autoReconnect_cancel;

  /// No description provided for @autoReconnect_message.
  ///
  /// In en, this message translates to:
  /// **'Reconnecting to device...'**
  String get autoReconnect_message;

  /// No description provided for @bottomNav_call.
  ///
  /// In en, this message translates to:
  /// **'Calls'**
  String get bottomNav_call;

  /// No description provided for @bottomNav_chat.
  ///
  /// In en, this message translates to:
  /// **'Chats'**
  String get bottomNav_chat;

  /// No description provided for @bottomNav_contacts.
  ///
  /// In en, this message translates to:
  /// **'Contacts'**
  String get bottomNav_contacts;

  /// No description provided for @bottomNav_profile.
  ///
  /// In en, this message translates to:
  /// **'Me'**
  String get bottomNav_profile;

  /// No description provided for @callList_searchHint.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get callList_searchHint;

  /// No description provided for @callList_title.
  ///
  /// In en, this message translates to:
  /// **'Call History'**
  String get callList_title;

  /// No description provided for @callList_noMembersError.
  ///
  /// In en, this message translates to:
  /// **'Insufficient group members to start a call'**
  String get callList_noMembersError;

  /// No description provided for @callList_loadMembersError.
  ///
  /// In en, this message translates to:
  /// **'Failed to load group members'**
  String get callList_loadMembersError;

  /// No description provided for @callList_selectCallType.
  ///
  /// In en, this message translates to:
  /// **'Select Call Type'**
  String get callList_selectCallType;

  /// No description provided for @callList_selectCallTypeDesc.
  ///
  /// In en, this message translates to:
  /// **'This group has 2 members, please select call type:'**
  String get callList_selectCallTypeDesc;

  /// No description provided for @callList_pttTalk.
  ///
  /// In en, this message translates to:
  /// **'PTT Talk'**
  String get callList_pttTalk;

  /// No description provided for @callList_voiceCall.
  ///
  /// In en, this message translates to:
  /// **'Voice Call'**
  String get callList_voiceCall;

  /// No description provided for @callList_voiceCallNotImplemented.
  ///
  /// In en, this message translates to:
  /// **'Voice call feature is under development...'**
  String get callList_voiceCallNotImplemented;

  /// No description provided for @callList_noRecords.
  ///
  /// In en, this message translates to:
  /// **'No call records'**
  String get callList_noRecords;

  /// No description provided for @callList_connected.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get callList_connected;

  /// No description provided for @callList_missed.
  ///
  /// In en, this message translates to:
  /// **'Missed'**
  String get callList_missed;

  /// No description provided for @callList_outgoing.
  ///
  /// In en, this message translates to:
  /// **'Outgoing'**
  String get callList_outgoing;

  /// No description provided for @callList_yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get callList_yesterday;

  /// No description provided for @voiceCall_calling.
  ///
  /// In en, this message translates to:
  /// **'Calling...'**
  String get voiceCall_calling;

  /// No description provided for @voiceCall_ended.
  ///
  /// In en, this message translates to:
  /// **'Call ended'**
  String get voiceCall_ended;

  /// No description provided for @voiceCall_demoMessage.
  ///
  /// In en, this message translates to:
  /// **'Voice call feature is under development. This is a demo interface.'**
  String get voiceCall_demoMessage;

  /// No description provided for @channelPicker_cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get channelPicker_cancel;

  /// No description provided for @channelPicker_channel.
  ///
  /// In en, this message translates to:
  /// **'Channel - {channel}'**
  String channelPicker_channel(Object channel);

  /// No description provided for @channelPicker_title.
  ///
  /// In en, this message translates to:
  /// **'Select Channel'**
  String get channelPicker_title;

  /// No description provided for @chatList_connectDevice.
  ///
  /// In en, this message translates to:
  /// **'Connect Device'**
  String get chatList_connectDevice;

  /// No description provided for @chatList_connectionFailed.
  ///
  /// In en, this message translates to:
  /// **'Device connection failed: {error}'**
  String chatList_connectionFailed(Object error);

  /// No description provided for @chatList_connectionSuccess.
  ///
  /// In en, this message translates to:
  /// **'Device connected: {device}'**
  String chatList_connectionSuccess(Object device);

  /// No description provided for @chatList_createGroup.
  ///
  /// In en, this message translates to:
  /// **'Create Group'**
  String get chatList_createGroup;

  /// No description provided for @chatList_disconnectDevice.
  ///
  /// In en, this message translates to:
  /// **'Disconnect'**
  String get chatList_disconnectDevice;

  /// No description provided for @chatList_disconnectFailed.
  ///
  /// In en, this message translates to:
  /// **'Disconnect failed: {error}'**
  String chatList_disconnectFailed(Object error);

  /// No description provided for @chatList_disconnectSuccess.
  ///
  /// In en, this message translates to:
  /// **'Device disconnected'**
  String get chatList_disconnectSuccess;

  /// No description provided for @chatList_joinGroup.
  ///
  /// In en, this message translates to:
  /// **'Join Group'**
  String get chatList_joinGroup;

  /// No description provided for @chatList_searchHint.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get chatList_searchHint;

  /// No description provided for @chatList_unpairFailed.
  ///
  /// In en, this message translates to:
  /// **'Unpair failed: {error}'**
  String chatList_unpairFailed(Object error);

  /// No description provided for @chatList_unpairSuccess.
  ///
  /// In en, this message translates to:
  /// **'Device unpaired'**
  String get chatList_unpairSuccess;

  /// No description provided for @createGroup_cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get createGroup_cancel;

  /// No description provided for @createGroup_done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get createGroup_done;

  /// No description provided for @createGroup_selectChannel.
  ///
  /// In en, this message translates to:
  /// **'Select Channel'**
  String get createGroup_selectChannel;

  /// No description provided for @createGroup_setGroupName.
  ///
  /// In en, this message translates to:
  /// **'Group Name'**
  String get createGroup_setGroupName;

  /// No description provided for @createGroup_setPassword.
  ///
  /// In en, this message translates to:
  /// **'Group Password'**
  String get createGroup_setPassword;

  /// No description provided for @createGroup_title.
  ///
  /// In en, this message translates to:
  /// **'Create Group'**
  String get createGroup_title;

  /// No description provided for @deviceInfo_activateTurMass.
  ///
  /// In en, this message translates to:
  /// **'Activate TurMass™'**
  String get deviceInfo_activateTurMass;

  /// No description provided for @deviceInfo_bleVersion.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth Firmware Version'**
  String get deviceInfo_bleVersion;

  /// No description provided for @deviceInfo_deviceBattery.
  ///
  /// In en, this message translates to:
  /// **'Battery'**
  String get deviceInfo_deviceBattery;

  /// No description provided for @deviceInfo_deviceId.
  ///
  /// In en, this message translates to:
  /// **'Device ID'**
  String get deviceInfo_deviceId;

  /// No description provided for @deviceInfo_deviceId_failed.
  ///
  /// In en, this message translates to:
  /// **'Failed to get'**
  String get deviceInfo_deviceId_failed;

  /// No description provided for @deviceInfo_deviceId_loading.
  ///
  /// In en, this message translates to:
  /// **'Loading…'**
  String get deviceInfo_deviceId_loading;

  /// No description provided for @deviceInfo_deviceInfo.
  ///
  /// In en, this message translates to:
  /// **'Device Info'**
  String get deviceInfo_deviceInfo;

  /// No description provided for @deviceInfo_deviceModel.
  ///
  /// In en, this message translates to:
  /// **'Device Model'**
  String get deviceInfo_deviceModel;

  /// No description provided for @deviceInfo_deviceName.
  ///
  /// In en, this message translates to:
  /// **'Device Name'**
  String get deviceInfo_deviceName;

  /// No description provided for @deviceInfo_deviceOperation.
  ///
  /// In en, this message translates to:
  /// **'Device Operation'**
  String get deviceInfo_deviceOperation;

  /// No description provided for @deviceInfo_deviceVersion.
  ///
  /// In en, this message translates to:
  /// **'Device Version'**
  String get deviceInfo_deviceVersion;

  /// No description provided for @deviceInfo_disconnect.
  ///
  /// In en, this message translates to:
  /// **'Disconnect Device'**
  String get deviceInfo_disconnect;

  /// No description provided for @deviceInfo_hwVersion.
  ///
  /// In en, this message translates to:
  /// **'Hardware Version'**
  String get deviceInfo_hwVersion;

  /// No description provided for @deviceInfo_title.
  ///
  /// In en, this message translates to:
  /// **'Device Info'**
  String get deviceInfo_title;

  /// No description provided for @deviceInfo_turMassFirmwareVersion.
  ///
  /// In en, this message translates to:
  /// **'TurMass™ Firmware Version'**
  String get deviceInfo_turMassFirmwareVersion;

  /// No description provided for @deviceInfo_unpair.
  ///
  /// In en, this message translates to:
  /// **'Unpair Device'**
  String get deviceInfo_unpair;

  /// No description provided for @global_appTitle.
  ///
  /// In en, this message translates to:
  /// **'aiTalk'**
  String get global_appTitle;

  /// No description provided for @global_cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get global_cancel;

  /// No description provided for @joinGroup_title.
  ///
  /// In en, this message translates to:
  /// **'Join Group'**
  String get joinGroup_title;

  /// No description provided for @notification_showContent.
  ///
  /// In en, this message translates to:
  /// **'Show message content'**
  String get notification_showContent;

  /// No description provided for @notification_showContent_desc.
  ///
  /// In en, this message translates to:
  /// **'Display message preview in notifications'**
  String get notification_showContent_desc;

  /// No description provided for @notification_system.
  ///
  /// In en, this message translates to:
  /// **'System notifications'**
  String get notification_system;

  /// No description provided for @notification_system_desc.
  ///
  /// In en, this message translates to:
  /// **'Receive system push notifications'**
  String get notification_system_desc;

  /// No description provided for @notification_voiceCall.
  ///
  /// In en, this message translates to:
  /// **'Voice call notifications'**
  String get notification_voiceCall;

  /// No description provided for @notification_voiceCall_desc.
  ///
  /// In en, this message translates to:
  /// **'Notify when there is an incoming voice call'**
  String get notification_voiceCall_desc;

  /// No description provided for @profile_avatar.
  ///
  /// In en, this message translates to:
  /// **'Avatar'**
  String get profile_avatar;

  /// No description provided for @profile_developerMode.
  ///
  /// In en, this message translates to:
  /// **'Developer Mode'**
  String get profile_developerMode;

  /// No description provided for @profile_deviceId.
  ///
  /// In en, this message translates to:
  /// **'Device ID: {id}'**
  String profile_deviceId(Object id);

  /// No description provided for @profile_deviceManagement.
  ///
  /// In en, this message translates to:
  /// **'Device Management'**
  String get profile_deviceManagement;

  /// No description provided for @profile_language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get profile_language;

  /// No description provided for @profile_languageChinese.
  ///
  /// In en, this message translates to:
  /// **'Chinese'**
  String get profile_languageChinese;

  /// No description provided for @profile_languageEnglish.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get profile_languageEnglish;

  /// No description provided for @profile_languageSystem.
  ///
  /// In en, this message translates to:
  /// **'System Default'**
  String get profile_languageSystem;

  /// No description provided for @profile_myInfo.
  ///
  /// In en, this message translates to:
  /// **'My Info'**
  String get profile_myInfo;

  /// No description provided for @profile_nickname.
  ///
  /// In en, this message translates to:
  /// **'Nickname'**
  String get profile_nickname;

  /// No description provided for @profile_nickname_default.
  ///
  /// In en, this message translates to:
  /// **'Default User'**
  String get profile_nickname_default;

  /// No description provided for @profile_qrcode.
  ///
  /// In en, this message translates to:
  /// **'QR Code'**
  String get profile_qrcode;

  /// No description provided for @profile_qrcode_title.
  ///
  /// In en, this message translates to:
  /// **'My QR Code'**
  String get profile_qrcode_title;

  /// No description provided for @profile_qrcode_scanHint.
  ///
  /// In en, this message translates to:
  /// **'Scan QR code to add friend'**
  String get profile_qrcode_scanHint;

  /// No description provided for @profile_qrcode_myInfo.
  ///
  /// In en, this message translates to:
  /// **'My Information'**
  String get profile_qrcode_myInfo;

  /// No description provided for @profile_qrcode_nickname.
  ///
  /// In en, this message translates to:
  /// **'Nickname'**
  String get profile_qrcode_nickname;

  /// No description provided for @profile_qrcode_deviceId.
  ///
  /// In en, this message translates to:
  /// **'Device ID'**
  String get profile_qrcode_deviceId;

  /// No description provided for @profile_qrcode_channelType.
  ///
  /// In en, this message translates to:
  /// **'Channel Type'**
  String get profile_qrcode_channelType;

  /// No description provided for @profile_qrcode_channelNumber.
  ///
  /// In en, this message translates to:
  /// **'Channel Number'**
  String get profile_qrcode_channelNumber;

  /// No description provided for @profile_qrcode_frequency.
  ///
  /// In en, this message translates to:
  /// **'Frequency'**
  String get profile_qrcode_frequency;

  /// No description provided for @profile_qrcode_rateMode.
  ///
  /// In en, this message translates to:
  /// **'Rate Mode'**
  String get profile_qrcode_rateMode;

  /// No description provided for @profile_qrcode_publicChannel.
  ///
  /// In en, this message translates to:
  /// **'Public Channel'**
  String get profile_qrcode_publicChannel;

  /// No description provided for @profile_qrcode_privateChannel.
  ///
  /// In en, this message translates to:
  /// **'Private Channel'**
  String get profile_qrcode_privateChannel;

  /// No description provided for @profile_qrcode_copyData.
  ///
  /// In en, this message translates to:
  /// **'Copy QR Code Data'**
  String get profile_qrcode_copyData;

  /// No description provided for @profile_qrcode_dataCopied.
  ///
  /// In en, this message translates to:
  /// **'QR code data copied to clipboard'**
  String get profile_qrcode_dataCopied;

  /// No description provided for @profile_qrcode_generateFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to generate QR code'**
  String get profile_qrcode_generateFailed;

  /// No description provided for @profile_qrcode_retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get profile_qrcode_retry;

  /// No description provided for @profile_qrcode_noData.
  ///
  /// In en, this message translates to:
  /// **'Unable to generate QR code'**
  String get profile_qrcode_noData;

  /// No description provided for @profile_qrcode_saveImage.
  ///
  /// In en, this message translates to:
  /// **'Save QR Code'**
  String get profile_qrcode_saveImage;

  /// No description provided for @profile_qrcode_imageSaved.
  ///
  /// In en, this message translates to:
  /// **'QR code saved to gallery'**
  String get profile_qrcode_imageSaved;

  /// No description provided for @profile_qrcode_saveFailed.
  ///
  /// In en, this message translates to:
  /// **'Save failed'**
  String get profile_qrcode_saveFailed;

  /// No description provided for @profile_settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get profile_settings;

  /// No description provided for @profile_signature.
  ///
  /// In en, this message translates to:
  /// **'Signature'**
  String get profile_signature;

  /// No description provided for @profile_userGuide.
  ///
  /// In en, this message translates to:
  /// **'User Guide'**
  String get profile_userGuide;

  /// No description provided for @publicChat_inputHint.
  ///
  /// In en, this message translates to:
  /// **'Type a message…'**
  String get publicChat_inputHint;

  /// Public chat interface, voice mode prompt
  ///
  /// In en, this message translates to:
  /// **'Press and hold to speak'**
  String get publicChat_pressHold;

  /// Public chat interface, recording state prompt
  ///
  /// In en, this message translates to:
  /// **'Recording...'**
  String get publicChat_recording;

  /// No description provided for @publicGroup_chatHistory.
  ///
  /// In en, this message translates to:
  /// **'Chat History'**
  String get publicGroup_chatHistory;

  /// No description provided for @publicGroup_chatInfo.
  ///
  /// In en, this message translates to:
  /// **'Chat Info'**
  String get publicGroup_chatInfo;

  /// No description provided for @publicGroup_deleteHistory.
  ///
  /// In en, this message translates to:
  /// **'Delete History'**
  String get publicGroup_deleteHistory;

  /// No description provided for @publicGroup_detail_title.
  ///
  /// In en, this message translates to:
  /// **'Group Details'**
  String get publicGroup_detail_title;

  /// No description provided for @publicGroup_editChannel.
  ///
  /// In en, this message translates to:
  /// **'Edit Channel'**
  String get publicGroup_editChannel;

  /// No description provided for @publicGroup_groupName.
  ///
  /// In en, this message translates to:
  /// **'Group Name'**
  String get publicGroup_groupName;

  /// No description provided for @publicGroup_name.
  ///
  /// In en, this message translates to:
  /// **'Public Group - {channel}'**
  String publicGroup_name(Object channel);

  /// No description provided for @publicGroup_searchHistory.
  ///
  /// In en, this message translates to:
  /// **'Search History'**
  String get publicGroup_searchHistory;

  /// No description provided for @privateGroup_cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get privateGroup_cancel;

  /// No description provided for @privateGroup_changePassword.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get privateGroup_changePassword;

  /// No description provided for @privateGroup_chatHistory.
  ///
  /// In en, this message translates to:
  /// **'Chat History'**
  String get privateGroup_chatHistory;

  /// No description provided for @privateGroup_chatInfo.
  ///
  /// In en, this message translates to:
  /// **'Chat Info'**
  String get privateGroup_chatInfo;

  /// No description provided for @privateGroup_deleteHistory.
  ///
  /// In en, this message translates to:
  /// **'Delete History'**
  String get privateGroup_deleteHistory;

  /// No description provided for @privateGroup_detail_title.
  ///
  /// In en, this message translates to:
  /// **'Group Details'**
  String get privateGroup_detail_title;

  /// No description provided for @privateGroup_done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get privateGroup_done;

  /// No description provided for @privateGroup_editChannel.
  ///
  /// In en, this message translates to:
  /// **'Edit Channel'**
  String get privateGroup_editChannel;

  /// No description provided for @privateGroup_groupChannel.
  ///
  /// In en, this message translates to:
  /// **'Group Channel'**
  String get privateGroup_groupChannel;

  /// No description provided for @privateGroup_groupMembers.
  ///
  /// In en, this message translates to:
  /// **'Group Members'**
  String get privateGroup_groupMembers;

  /// No description provided for @privateGroup_groupName.
  ///
  /// In en, this message translates to:
  /// **'Group Name'**
  String get privateGroup_groupName;

  /// No description provided for @privateGroup_groupPassword.
  ///
  /// In en, this message translates to:
  /// **'Group Password'**
  String get privateGroup_groupPassword;

  /// No description provided for @privateGroup_groupQRCode.
  ///
  /// In en, this message translates to:
  /// **'Group QR Code'**
  String get privateGroup_groupQRCode;

  /// No description provided for @privateGroup_leaveGroup.
  ///
  /// In en, this message translates to:
  /// **'Disband Group'**
  String get privateGroup_leaveGroup;

  /// No description provided for @privateGroup_searchHistory.
  ///
  /// In en, this message translates to:
  /// **'Search History'**
  String get privateGroup_searchHistory;

  /// No description provided for @settings_about.
  ///
  /// In en, this message translates to:
  /// **'About aiTalk'**
  String get settings_about;

  /// No description provided for @settings_chatSettings.
  ///
  /// In en, this message translates to:
  /// **'Chat Settings'**
  String get settings_chatSettings;

  /// No description provided for @settings_displaySettings.
  ///
  /// In en, this message translates to:
  /// **'Display Settings'**
  String get settings_displaySettings;

  /// No description provided for @settings_fontSize.
  ///
  /// In en, this message translates to:
  /// **'Font Size'**
  String get settings_fontSize;

  /// No description provided for @settings_helpAndFeedback.
  ///
  /// In en, this message translates to:
  /// **'Help & Feedback'**
  String get settings_helpAndFeedback;

  /// No description provided for @settings_multilanguage.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get settings_multilanguage;

  /// No description provided for @settings_notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get settings_notifications;

  /// No description provided for @settings_other.
  ///
  /// In en, this message translates to:
  /// **'Others'**
  String get settings_other;

  /// No description provided for @settings_storageManagement.
  ///
  /// In en, this message translates to:
  /// **'Storage Management'**
  String get settings_storageManagement;

  /// No description provided for @settings_storageSettings.
  ///
  /// In en, this message translates to:
  /// **'Storage Settings'**
  String get settings_storageSettings;

  /// No description provided for @settings_themeDark.
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get settings_themeDark;

  /// No description provided for @settings_themeLight.
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get settings_themeLight;

  /// No description provided for @settings_themeMode.
  ///
  /// In en, this message translates to:
  /// **'Theme Mode'**
  String get settings_themeMode;

  /// No description provided for @settings_themeSystem.
  ///
  /// In en, this message translates to:
  /// **'System Default'**
  String get settings_themeSystem;

  /// No description provided for @settings_title.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings_title;

  /// No description provided for @settings_version.
  ///
  /// In en, this message translates to:
  /// **'Version {version}'**
  String settings_version(Object version);

  /// No description provided for @settings_voicePlayback.
  ///
  /// In en, this message translates to:
  /// **'Voice Playback'**
  String get settings_voicePlayback;

  /// No description provided for @voicePlayback_autoPlay.
  ///
  /// In en, this message translates to:
  /// **'Auto play voice'**
  String get voicePlayback_autoPlay;

  /// No description provided for @voicePlayback_autoPlay_desc.
  ///
  /// In en, this message translates to:
  /// **'Automatically play received voice messages'**
  String get voicePlayback_autoPlay_desc;

  /// No description provided for @voicePlayback_backgroundMode.
  ///
  /// In en, this message translates to:
  /// **'Background playback mode'**
  String get voicePlayback_backgroundMode;

  /// No description provided for @voicePlayback_backgroundMode_desc.
  ///
  /// In en, this message translates to:
  /// **'Continue playing voice messages when app is in background'**
  String get voicePlayback_backgroundMode_desc;

  /// Label for PTT talk action in chat plus menu
  ///
  /// In en, this message translates to:
  /// **'PTT Talk'**
  String get chatAction_ptt;

  /// Label for voice call action in chat plus menu
  ///
  /// In en, this message translates to:
  /// **'Voice Call'**
  String get chatAction_voiceCall;

  /// Waiting for answer text in voice call dialing screen
  ///
  /// In en, this message translates to:
  /// **'Waiting for answer...'**
  String get voiceCall_waitingAnswer;

  /// Hang up button in voice call screen
  ///
  /// In en, this message translates to:
  /// **'Hang Up'**
  String get voiceCall_hangUp;

  /// Speaker button in voice call screen
  ///
  /// In en, this message translates to:
  /// **'Speaker'**
  String get voiceCall_speaker;

  /// Earpiece mode in voice call screen
  ///
  /// In en, this message translates to:
  /// **'Earpiece'**
  String get voiceCall_earpiece;

  /// Title for incoming call screen
  ///
  /// In en, this message translates to:
  /// **'Incoming Call'**
  String get voiceCall_incomingCall;

  /// Call type indicator in incoming call screen
  ///
  /// In en, this message translates to:
  /// **'Real-time Call'**
  String get voiceCall_realTimeCall;

  /// Accept button in incoming call screen
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get voiceCall_accept;

  /// Reject button in incoming call screen
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get voiceCall_reject;

  /// Placeholder shown when there are no group members in PTT screen
  ///
  /// In en, this message translates to:
  /// **'No members'**
  String get ptt_noMembers;

  /// No description provided for @storage_totalTitle.
  ///
  /// In en, this message translates to:
  /// **'aiTalk Data'**
  String get storage_totalTitle;

  /// No description provided for @storage_cache.
  ///
  /// In en, this message translates to:
  /// **'Cache'**
  String get storage_cache;

  /// No description provided for @storage_cacheDesc.
  ///
  /// In en, this message translates to:
  /// **'Cache is used to temporarily store data generated during the app\'s operation, such as images, file previews, and temporary messages. Clearing the cache can free up storage space without affecting your chat history or personal settings.'**
  String get storage_cacheDesc;

  /// No description provided for @storage_cacheClear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get storage_cacheClear;

  /// No description provided for @storage_cacheCleared.
  ///
  /// In en, this message translates to:
  /// **'Cache cleared'**
  String get storage_cacheCleared;

  /// No description provided for @storage_chathistory.
  ///
  /// In en, this message translates to:
  /// **'Chat History'**
  String get storage_chathistory;

  /// No description provided for @storage_chathistoryDesc.
  ///
  /// In en, this message translates to:
  /// **'Chat History contains all your conversations with other users, including text, images, and files. Deleting chat history will permanently delete all conversation content and cannot be undone. Please proceed with caution.'**
  String get storage_chathistoryDesc;

  /// No description provided for @storage_chathistoryManage.
  ///
  /// In en, this message translates to:
  /// **'Manage'**
  String get storage_chathistoryManage;

  /// No description provided for @storage_appdata.
  ///
  /// In en, this message translates to:
  /// **'App Data'**
  String get storage_appdata;

  /// No description provided for @storage_appdataDesc.
  ///
  /// In en, this message translates to:
  /// **'App Data refers to the storage space occupied by the application itself, including essential runtime files and model parameters for voice algorithms. These data are required for the app to function properly and cannot be deleted.'**
  String get storage_appdataDesc;

  /// No description provided for @settings_textSize.
  ///
  /// In en, this message translates to:
  /// **'Text Size'**
  String get settings_textSize;

  /// No description provided for @textSize_demoMessage.
  ///
  /// In en, this message translates to:
  /// **'I\'m {name}'**
  String textSize_demoMessage(Object name);

  /// No description provided for @chatList_voicePreview.
  ///
  /// In en, this message translates to:
  /// **'[Voice]'**
  String get chatList_voicePreview;

  /// No description provided for @chatList_locationPreview.
  ///
  /// In en, this message translates to:
  /// **'[Location]'**
  String get chatList_locationPreview;

  /// No description provided for @chatList_messagePreview.
  ///
  /// In en, this message translates to:
  /// **'[Message]'**
  String get chatList_messagePreview;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
