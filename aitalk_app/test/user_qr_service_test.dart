import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo_ios/core/models/user_qr_data.dart';

void main() {
  group('UserQrData', () {
    test('should create QR data correctly', () {
      const qrData = UserQrData(
        frequency: 483600000,
        rateMode: 6,
        nickname: '测试用户',
        deviceId: '0x12345678',
      );

      expect(qrData.frequency, 483600000);
      expect(qrData.rateMode, 6);
      expect(qrData.nickname, '测试用户');
      expect(qrData.deviceId, '0x12345678');
    });

    test('should convert to QR string correctly', () {
      const qrData = UserQrData(
        frequency: 483600000,
        rateMode: 6,
        nickname: '测试用户',
        deviceId: '0x12345678',
      );

      final qrString = qrData.toQrString();
      expect(qrString, isNotEmpty);
      expect(qrString, contains('483600000'));
      expect(qrString, contains('6'));
      expect(qrString, contains('测试用户'));
      expect(qrString, contains('0x12345678'));
    });

    test('should parse from QR string correctly', () {
      const originalData = UserQrData(
        frequency: 483600000,
        rateMode: 6,
        nickname: '测试用户',
        deviceId: '0x12345678',
      );

      final qrString = originalData.toQrString();
      final parsedData = UserQrData.fromQrString(qrString);

      expect(parsedData, isNotNull);
      expect(parsedData!.frequency, originalData.frequency);
      expect(parsedData.rateMode, originalData.rateMode);
      expect(parsedData.nickname, originalData.nickname);
      expect(parsedData.deviceId, originalData.deviceId);
    });

    test('should return null for invalid QR string', () {
      const invalidQrString = 'invalid json';
      final parsedData = UserQrData.fromQrString(invalidQrString);
      expect(parsedData, isNull);
    });

    test('should format frequency display text correctly', () {
      const qrData = UserQrData(
        frequency: 483600000,
        rateMode: 6,
        nickname: '测试用户',
        deviceId: '0x12345678',
      );

      expect(qrData.frequencyDisplayText, '483.60 MHz');
    });

    test('should format rate mode display text correctly', () {
      const qrData = UserQrData(
        frequency: 483600000,
        rateMode: 6,
        nickname: '测试用户',
        deviceId: '0x12345678',
      );

      expect(qrData.rateModeDisplayText, '速率模式 6');
    });
  });
}
