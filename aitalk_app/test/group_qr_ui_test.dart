import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo_ios/core/models/user_qr_data.dart';
import 'package:flutter_demo_ios/views/chats/group_qr_code_screen.dart';

void main() {
  group('GroupQrCodeScreen UI Tests', () {
    testWidgets('should display group information correctly', (WidgetTester tester) async {
      // 创建测试用的群组二维码数据
      final testGroupData = GroupQrData(
        groupId: 'TEST12345',
        groupName: '测试群组',
        password: 123456,
        channel: 5,
        members: {
          0: '0x12345678', // 群主
          1: '0x87654321', // 成员1
          2: '0xABCDEF00', // 成员2
          3: '0x11223344', // 成员3
        },
      );

      // 构建测试应用
      await tester.pumpWidget(
        MaterialApp(
          home: GroupQrCodeScreen(
            groupId: testGroupData.groupId,
            groupName: testGroupData.groupName,
          ),
        ),
      );

      // 等待加载完成
      await tester.pumpAndSettle();

      // 验证基本信息显示
      expect(find.text('Group Information'), findsOneWidget);
      expect(find.text('测试群组'), findsOneWidget);
      expect(find.text('TEST12345'), findsOneWidget);
      expect(find.text('Channel 5'), findsOneWidget);
      expect(find.text('123456'), findsOneWidget);
      expect(find.text('4 members'), findsOneWidget);
    });

    testWidgets('should display member list with correct formatting', (WidgetTester tester) async {
      // 创建测试用的群组二维码数据
      final testGroupData = GroupQrData(
        groupId: 'TEST12345',
        groupName: '测试群组',
        password: 0, // 无密码
        channel: 3,
        members: {
          0: '0x12345678', // 群主
          1: '0x87654321', // 成员1
        },
      );

      // 构建测试应用
      await tester.pumpWidget(
        MaterialApp(
          home: GroupQrCodeScreen(
            groupId: testGroupData.groupId,
            groupName: testGroupData.groupName,
          ),
        ),
      );

      // 等待加载完成
      await tester.pumpAndSettle();

      // 验证成员列表标题
      expect(find.text('Members (2)'), findsOneWidget);
      
      // 验证无密码显示
      expect(find.text('No Password'), findsOneWidget);
      
      // 验证群主标识
      expect(find.text('Owner'), findsOneWidget);
      
      // 验证设备ID显示
      expect(find.text('0x12345678'), findsOneWidget);
      expect(find.text('0x87654321'), findsOneWidget);
    });

    test('should format member count correctly', () {
      // 测试单个成员
      final singleMember = GroupQrData(
        groupId: 'TEST1',
        groupName: '单人群',
        password: 0,
        channel: 1,
        members: {0: '0x12345678'},
      );
      expect(singleMember.memberCount, 1);

      // 测试多个成员
      final multipleMembers = GroupQrData(
        groupId: 'TEST2',
        groupName: '多人群',
        password: 0,
        channel: 2,
        members: {
          0: '0x12345678',
          1: '0x87654321',
          2: '0xABCDEF00',
          3: '0x11223344',
          4: '0x55667788',
        },
      );
      expect(multipleMembers.memberCount, 5);

      // 测试空成员列表
      final emptyMembers = GroupQrData(
        groupId: 'TEST3',
        groupName: '空群',
        password: 0,
        channel: 3,
        members: {},
      );
      expect(emptyMembers.memberCount, 0);
    });

    test('should identify group owner correctly', () {
      final groupData = GroupQrData(
        groupId: 'TEST123',
        groupName: '测试群',
        password: 123,
        channel: 5,
        members: {
          0: '0x12345678', // 群主 (memberId = 0)
          1: '0x87654321', // 普通成员
          2: '0xABCDEF00', // 普通成员
        },
      );

      // 验证群主是memberId为0的成员
      expect(groupData.members[0], '0x12345678');
      expect(groupData.members.containsKey(0), true);
    });

    test('should handle different password types', () {
      // 无密码群组
      final noPasswordGroup = GroupQrData(
        groupId: 'TEST1',
        groupName: '无密码群',
        password: 0,
        channel: 1,
        members: {0: '0x12345678'},
      );
      expect(noPasswordGroup.passwordDisplayText, 'No Password');

      // 有密码群组
      final passwordGroup = GroupQrData(
        groupId: 'TEST2',
        groupName: '有密码群',
        password: 123456,
        channel: 2,
        members: {0: '0x12345678'},
      );
      expect(passwordGroup.passwordDisplayText, '123456');

      // 大数字密码
      final bigPasswordGroup = GroupQrData(
        groupId: 'TEST3',
        groupName: '大密码群',
        password: 999999999,
        channel: 3,
        members: {0: '0x12345678'},
      );
      expect(bigPasswordGroup.passwordDisplayText, '999999999');
    });

    test('should handle member ID ranges correctly', () {
      final groupData = GroupQrData(
        groupId: 'TEST123',
        groupName: '测试群',
        password: 0,
        channel: 1,
        members: {
          0: '0x12345678',   // 群主
          1: '0x87654321',   // 成员1
          15: '0xABCDEF00',  // 成员15 (最大memberId)
          255: '0x11223344', // 成员255 (1字节最大值)
        },
      );

      expect(groupData.memberCount, 4);
      expect(groupData.members[0], '0x12345678');
      expect(groupData.members[1], '0x87654321');
      expect(groupData.members[15], '0xABCDEF00');
      expect(groupData.members[255], '0x11223344');
    });
  });
}
